# Eneco Chatbot Widget

A self-contained React chatbot widget that can be embedded into any website via a single JavaScript file.

## Features

- ✅ **Single File Bundle**: Everything bundled into one JavaScript file
- ✅ **Shadow DOM Isolation**: Prevents CSS conflicts with host page
- ✅ **Zero Dependencies**: No external dependencies required on host page
- ✅ **Global Configuration**: Configure via `window.MyChatbotSettings`
- ✅ **Multiple View Modes**: Minimized, chatbox, and fullscreen modes
- ✅ **Responsive Design**: Works on desktop, tablet, and mobile
- ✅ **Asset Inlining**: All images and styles bundled inline

## Quick Start

### 1. Build the Widget

```bash
# Build the widget bundle
npm run build:widget
```

This creates `dist/eneco-chatbot-widget.iife.js` - a single JavaScript file containing everything.

### 2. Embed in Your Website

```html
<!DOCTYPE html>
<html>
<head>
    <title>Your Website</title>
</head>
<body>
    <!-- Your website content -->
    <h1>Welcome to our website</h1>
    <p>Your content here...</p>

    <!-- Optional: Configure the chatbot -->
    <script>
        window.MyChatbotSettings = {
            assistantName: 'Edwin',
            assistantTitle: 'Eneco Assistant',
            theme: 'light',
            autoOpen: false,
            apiUrl: 'https://your-api.com',
            customerId: 'your-customer-id',
            accountId: 'your-account-id'
        };
    </script>

    <!-- Load the chatbot widget -->
    <script src="path/to/eneco-chatbot-widget.iife.js"></script>
</body>
</html>
```

## Configuration Options

Configure the chatbot by setting `window.MyChatbotSettings` before loading the script:

```javascript
window.MyChatbotSettings = {
    // API Configuration
    apiUrl: 'https://your-api.com',
    customerId: 'your-customer-id',
    accountId: 'your-account-id',
    
    // UI Configuration
    assistantName: 'Edwin',                    // Assistant name
    assistantTitle: 'Eneco Assistant',        // Assistant title
    placeholder: 'Ask me anything...',        // Input placeholder
    theme: 'light',                           // 'light' or 'dark'
    position: 'bottom-right',                 // 'bottom-right' or 'bottom-left'
    
    // Behavior
    autoOpen: false,                          // Auto-open chat on load
    enableFullscreen: true,                   // Enable fullscreen mode
    zIndex: 9999,                            // CSS z-index
};
```

## Programmatic Control

After the widget loads, you can control it programmatically:

```javascript
// Initialize the chatbot
window.EnecoChatbot.init();

// Destroy the chatbot
window.EnecoChatbot.destroy();

// Update configuration
window.EnecoChatbot.updateConfig({
    theme: 'dark',
    autoOpen: true
});

// Get current configuration
const config = window.EnecoChatbot.getConfig();

// Check if initialized
const isInitialized = window.EnecoChatbot.isInitialized();
```

## Development

### Build Commands

```bash
# Regular app development
npm run dev

# Build regular app
npm run build

# Build widget bundle
npm run build:widget

# Test widget locally
npm run test:widget
```

### Testing the Widget

1. Build the widget: `npm run build:widget`
2. Open `test-widget.html` in a browser
3. Use the control buttons to test widget functionality

### File Structure

```
src/
├── widget.tsx                    # Widget entry point
├── components/
│   ├── Chat/                     # Chat components
│   └── Widget/
│       └── WidgetWrapper.tsx     # Shadow DOM wrapper
├── styles/                       # All styles (bundled inline)
└── ...

vite-plugins/
└── inline-assets.ts              # Custom Vite plugin for asset inlining

test-widget.html                  # Test page for widget
```

## How It Works

1. **Entry Point**: `src/widget.tsx` is the main entry point
2. **Shadow DOM**: Creates isolated DOM container to prevent CSS conflicts
3. **Asset Inlining**: Custom Vite plugin inlines all CSS and images
4. **Global API**: Exposes `window.EnecoChatbot` for programmatic control
5. **Auto-initialization**: Automatically initializes when DOM is ready

## Browser Support

- Chrome 53+
- Firefox 63+
- Safari 10+
- Edge 79+

## Bundle Size

The widget bundle is optimized for size:
- Minified and compressed
- Tree-shaken to remove unused code
- Assets inlined as base64 data URLs
- Typical size: ~200-400KB (depending on assets)

## Deployment

1. Build the widget: `npm run build:widget`
2. Upload `dist/eneco-chatbot-widget.iife.js` to your CDN
3. Include the script tag on target websites
4. Optionally set global configuration

## Troubleshooting

### Widget not appearing
- Check browser console for errors
- Verify script is loading correctly
- Check if `window.EnecoChatbot` exists

### CSS conflicts
- The widget uses Shadow DOM for isolation
- If issues persist, check z-index configuration

### API errors
- Verify API URL and credentials in configuration
- Check network tab for failed requests

## License

MIT License - see LICENSE file for details.
