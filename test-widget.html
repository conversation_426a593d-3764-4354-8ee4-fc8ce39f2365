<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eneco Chatbot Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5rem;
        }
        
        .content {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button.danger {
            background: #f44336;
        }
        
        button.danger:hover {
            background: #da190b;
        }
        
        .config-section {
            margin-top: 30px;
        }
        
        .config-section h3 {
            margin-bottom: 15px;
        }
        
        .config-option {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select {
            width: 100%;
            max-width: 300px;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .status {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .demo-content {
            height: 200vh;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Eneco Chatbot Widget Test</h1>
        
        <div class="content">
            <h2>Widget Controls</h2>
            <div class="controls">
                <button onclick="initializeChatbot()">Initialize Chatbot</button>
                <button onclick="destroyChatbot()" class="danger">Destroy Chatbot</button>
                <button onclick="checkStatus()">Check Status</button>
                <button onclick="updateConfig()">Update Config</button>
            </div>
            
            <div class="config-section">
                <h3>Configuration Options</h3>
                <div class="config-option">
                    <label for="assistantName">Assistant Name:</label>
                    <input type="text" id="assistantName" value="Edwin" />
                </div>
                <div class="config-option">
                    <label for="theme">Theme:</label>
                    <select id="theme">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                    </select>
                </div>
                <div class="config-option">
                    <label for="position">Position:</label>
                    <select id="position">
                        <option value="bottom-right">Bottom Right</option>
                        <option value="bottom-left">Bottom Left</option>
                    </select>
                </div>
                <div class="config-option">
                    <label for="autoOpen">Auto Open:</label>
                    <select id="autoOpen">
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                    </select>
                </div>
            </div>
            
            <div class="status" id="status">
                Status: Widget not loaded
            </div>
        </div>
        
        <div class="demo-content">
            <div>
                <h3>Demo Page Content</h3>
                <p>This is a test page to demonstrate the chatbot widget integration.</p>
                <p>The chatbot should appear as a floating button and not interfere with this content.</p>
                <p>Scroll down to see more content and test the widget behavior.</p>
            </div>
        </div>
    </div>

    <!-- Global Configuration -->
    <script>
        // Set global configuration before loading the widget
        window.MyChatbotSettings = {
            assistantName: 'Edwin',
            assistantTitle: 'Eneco Assistant',
            placeholder: 'Ask me anything about Eneco...',
            theme: 'light',
            position: 'bottom-right',
            enableFullscreen: true,
            autoOpen: false,
            zIndex: 9999,
            apiUrl: 'https://bot-v1.acc.api-digital.enecogroup.com/api/eneco',
            customerId: 'test-customer',
            accountId: 'test-account'
        };

        // Widget control functions
        function initializeChatbot() {
            if (window.EnecoChatbot) {
                window.EnecoChatbot.init();
                updateStatus('Widget initialized');
            } else {
                updateStatus('Widget script not loaded');
            }
        }

        function destroyChatbot() {
            if (window.EnecoChatbot) {
                window.EnecoChatbot.destroy();
                updateStatus('Widget destroyed');
            } else {
                updateStatus('Widget script not loaded');
            }
        }

        function checkStatus() {
            if (window.EnecoChatbot) {
                const isInitialized = window.EnecoChatbot.isInitialized();
                const config = window.EnecoChatbot.getConfig();
                updateStatus(`Widget loaded. Initialized: ${isInitialized}. Config: ${JSON.stringify(config, null, 2)}`);
            } else {
                updateStatus('Widget script not loaded');
            }
        }

        function updateConfig() {
            if (window.EnecoChatbot) {
                const newConfig = {
                    assistantName: document.getElementById('assistantName').value,
                    theme: document.getElementById('theme').value,
                    position: document.getElementById('position').value,
                    autoOpen: document.getElementById('autoOpen').value === 'true'
                };
                
                window.EnecoChatbot.updateConfig(newConfig);
                updateStatus(`Config updated: ${JSON.stringify(newConfig, null, 2)}`);
            } else {
                updateStatus('Widget script not loaded');
            }
        }

        function updateStatus(message) {
            document.getElementById('status').innerHTML = `Status: ${message}`;
        }

        // Check if widget is loaded on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.EnecoChatbot) {
                    updateStatus('Widget script loaded successfully');
                } else {
                    updateStatus('Widget script failed to load');
                }
            }, 1000);
        });
    </script>

    <!-- Load the widget script -->
    <script src="./dist/eneco-chatbot-widget.iife.js"></script>
</body>
</html>
