// Typography Tokens

// Font families
$font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
$font-family-mono: 'Fira Code', 'Monaco', 'Cascadia Code', 'Segoe UI Mono', 'Roboto Mono', 'Oxygen Mono', 'Ubuntu Monospace', 'Source Code Pro', 'Fira Mono', 'Droid Sans Mono', 'Courier New', monospace;

// Font weights
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// Font sizes
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px

// Line heights
$line-height-tight: 1.25;
$line-height-snug: 1.375;
$line-height-normal: 1.5;
$line-height-relaxed: 1.625;
$line-height-loose: 2;

// Letter spacing
$letter-spacing-tighter: -0.05em;
$letter-spacing-tight: -0.025em;
$letter-spacing-normal: 0;
$letter-spacing-wide: 0.025em;
$letter-spacing-wider: 0.05em;
$letter-spacing-widest: 0.1em;

// CSS Custom Properties for Typography
:root {
  --font-family-sans: #{$font-family-sans};
  --font-family-mono: #{$font-family-mono};
  
  --font-weight-light: #{$font-weight-light};
  --font-weight-normal: #{$font-weight-normal};
  --font-weight-medium: #{$font-weight-medium};
  --font-weight-semibold: #{$font-weight-semibold};
  --font-weight-bold: #{$font-weight-bold};
  
  --font-size-xs: #{$font-size-xs};
  --font-size-sm: #{$font-size-sm};
  --font-size-base: #{$font-size-base};
  --font-size-lg: #{$font-size-lg};
  --font-size-xl: #{$font-size-xl};
  --font-size-2xl: #{$font-size-2xl};
  --font-size-3xl: #{$font-size-3xl};
  --font-size-4xl: #{$font-size-4xl};
  
  --line-height-tight: #{$line-height-tight};
  --line-height-snug: #{$line-height-snug};
  --line-height-normal: #{$line-height-normal};
  --line-height-relaxed: #{$line-height-relaxed};
  --line-height-loose: #{$line-height-loose};
  
  --letter-spacing-tighter: #{$letter-spacing-tighter};
  --letter-spacing-tight: #{$letter-spacing-tight};
  --letter-spacing-normal: #{$letter-spacing-normal};
  --letter-spacing-wide: #{$letter-spacing-wide};
  --letter-spacing-wider: #{$letter-spacing-wider};
  --letter-spacing-widest: #{$letter-spacing-widest};
}


