// Color Tokens
// Primary colors
$primary-50: #eff6ff;
$primary-100: #dbeafe;
$primary-200: #bfdbfe;
$primary-300: #93c5fd;
$primary-400: #60a5fa;
$primary-500: #3b82f6;
$primary-600: #2563eb;
$primary-700: #1d4ed8;
$primary-800: #1e40af;
$primary-900: #1e3a8a;

// Gray colors
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Success colors
$success-50: #f0fdf4;
$success-100: #dcfce7;
$success-200: #bbf7d0;
$success-300: #86efac;
$success-400: #4ade80;
$success-500: #22c55e;
$success-600: #16a34a;
$success-700: #15803d;
$success-800: #166534;
$success-900: #14532d;

// Error colors
$error-50: #fef2f2;
$error-100: #fee2e2;
$error-200: #fecaca;
$error-300: #fca5a5;
$error-400: #f87171;
$error-500: #ef4444;
$error-600: #dc2626;
$error-700: #b91c1c;
$error-800: #991b1b;
$error-900: #7f1d1d;

// Warning colors
$warning-50: #fffbeb;
$warning-100: #fef3c7;
$warning-200: #fde68a;
$warning-300: #fcd34d;
$warning-400: #fbbf24;
$warning-500: #f59e0b;
$warning-600: #d97706;
$warning-700: #b45309;
$warning-800: #92400e;
$warning-900: #78350f;

// Chat specific colors
$chat-user-bg: $primary-500;
$chat-agent-bg: $gray-100;
$chat-user-text: white;
$chat-agent-text: $gray-900;

// Connection status colors
$status-connected: $success-500;
$status-connecting: $warning-500;
$status-disconnected: $gray-400;
$status-error: $error-500;

// Theme color mappings
:root {
  // Light theme (default)
  --color-background: #{$gray-50};
  --color-surface: white;
  --color-surface-secondary: #{$gray-100};
  --color-border: #{$gray-200};
  --color-border-hover: #{$gray-300};
  
  --color-text-primary: #{$gray-900};
  --color-text-secondary: #{$gray-600};
  --color-text-muted: #{$gray-400};
  
  --color-primary: #{$primary-500};
  --color-primary-hover: #{$primary-600};
  --color-primary-light: #{$primary-100};
  
  --color-success: #{$success-500};
  --color-error: #{$error-500};
  --color-warning: #{$warning-500};
  
  // Chat colors
  --color-chat-user-bg: #{$chat-user-bg};
  --color-chat-agent-bg: #{$chat-agent-bg};
  --color-chat-user-text: #{$chat-user-text};
  --color-chat-agent-text: #{$chat-agent-text};
  
  // Status colors
  --color-status-connected: #{$status-connected};
  --color-status-connecting: #{$status-connecting};
  --color-status-disconnected: #{$status-disconnected};
  --color-status-error: #{$status-error};
}

// Dark theme
[data-theme="dark"] {
  --color-background: #{$gray-900};
  --color-surface: #{$gray-800};
  --color-surface-secondary: #{$gray-700};
  --color-border: #{$gray-600};
  --color-border-hover: #{$gray-500};
  
  --color-text-primary: #{$gray-100};
  --color-text-secondary: #{$gray-300};
  --color-text-muted: #{$gray-500};
  
  --color-primary: #{$primary-400};
  --color-primary-hover: #{$primary-300};
  --color-primary-light: #{$primary-900};
  
  --color-success: #{$success-400};
  --color-error: #{$error-400};
  --color-warning: #{$warning-400};
  
  // Chat colors for dark theme
  --color-chat-user-bg: #{$primary-600};
  --color-chat-agent-bg: #{$gray-700};
  --color-chat-user-text: white;
  --color-chat-agent-text: #{$gray-100};
  
  // Status colors remain the same
  --color-status-connected: #{$status-connected};
  --color-status-connecting: #{$status-connecting};
  --color-status-disconnected: #{$gray-500};
  --color-status-error: #{$status-error};
}
