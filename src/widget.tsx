// Widget Entry Point - Creates a self-contained chatbot widget
import { ShadowDOMWrapper } from './components/Widget/WidgetWrapper';

// Global configuration interface
interface ChatbotConfig {
  apiUrl?: string;
  customerId?: string;
  accountId?: string;
  assistantName?: string;
  assistantTitle?: string;
  placeholder?: string;
  theme?: 'light' | 'dark';
  position?: 'bottom-right' | 'bottom-left';
  enableFullscreen?: boolean;
  autoOpen?: boolean;
  zIndex?: number;
}

// Default configuration
const defaultConfig: ChatbotConfig = {
  assistantName: 'Edwin',
  assistantTitle: 'Eneco Assistant',
  placeholder: 'Ask me anything about Eneco...',
  theme: 'light',
  position: 'bottom-right',
  enableFullscreen: true,
  autoOpen: false,
  zIndex: 9999,
  apiUrl: 'https://bot-v1.acc.api-digital.enecogroup.com',
  customerId: 'widget-user',
  accountId: 'widget-account',
};

// Widget manager class using Shadow DOM wrapper

class ChatbotWidgetManager {
  private shadowWrapper: ShadowDOMWrapper | null = null;
  private config: ChatbotConfig;

  constructor(config: ChatbotConfig = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  // Initialize the widget
  public init(): void {
    if (this.shadowWrapper?.isMounted()) {
      console.warn('Chatbot widget already initialized');
      return;
    }

    try {
      this.shadowWrapper = new ShadowDOMWrapper(this.config);
      this.shadowWrapper.mount();

      console.log('Eneco Chatbot Widget initialized successfully');
    } catch (error) {
      console.error('Failed to initialize chatbot widget:', error);
    }
  }

  // Destroy the widget
  public destroy(): void {
    if (this.shadowWrapper) {
      this.shadowWrapper.unmount();
      this.shadowWrapper = null;
    }
  }

  // Update configuration
  public updateConfig(newConfig: Partial<ChatbotConfig>): void {
    this.config = { ...this.config, ...newConfig };

    if (this.shadowWrapper) {
      this.shadowWrapper.updateConfig(this.config);
    }
  }

  // Get current configuration
  public getConfig(): ChatbotConfig {
    return { ...this.config };
  }

  // Check if initialized
  public isInitialized(): boolean {
    return this.shadowWrapper?.isMounted() || false;
  }
}

// Global initialization function
function initializeChatbot(): void {
  // Check if already initialized
  if ((window as any).EnecoChatbot) {
    console.warn('Eneco Chatbot already initialized');
    return;
  }

  // Get configuration from multiple possible global objects
  const globalConfig = {
    ...(window as any).EnecoChatbotSettings,
    ...(window as any).MyChatbotSettings,
    ...(window as any).ChatbotConfig,
  };
  
  // Create widget manager
  const chatbot = new ChatbotWidgetManager(globalConfig);
  
  // Expose to global scope
  (window as any).EnecoChatbot = {
    init: () => chatbot.init(),
    destroy: () => chatbot.destroy(),
    updateConfig: (config: Partial<ChatbotConfig>) => chatbot.updateConfig(config),
    getConfig: () => chatbot.getConfig(),
    isInitialized: () => chatbot.isInitialized(),
  };

  // Auto-initialize if DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => chatbot.init());
  } else {
    chatbot.init();
  }
}

// Auto-initialize when script loads
initializeChatbot();

// Export for module usage
export { ChatbotWidgetManager, initializeChatbot };
export type { ChatbotConfig };
