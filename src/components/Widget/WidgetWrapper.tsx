// Widget Wrapper Component - Handles Shadow DOM and isolation
import React, { useEffect, createContext, useContext } from 'react';
import { createRoot, type Root } from 'react-dom/client';
import ChatContainer from '../Chat/ChatContainer';
import FloatingChatButton from '../Chat/FloatingChatButton';
import QueryProvider from '../../providers/QueryProvider';
import { useChatStore } from '../../store';
import { VIEW_MODE } from '../../types';

// Styles will be injected at build time

interface WidgetConfig {
  apiUrl?: string;
  customerId?: string;
  accountId?: string;
  assistantName?: string;
  assistantTitle?: string;
  placeholder?: string;
  theme?: 'light' | 'dark';
  position?: 'bottom-right' | 'bottom-left';
  enableFullscreen?: boolean;
  autoOpen?: boolean;
  zIndex?: number;
}

// Widget Configuration Context
const WidgetConfigContext = createContext<WidgetConfig | null>(null);

// Hook to use widget configuration
export const useWidgetConfig = () => {
  const config = useContext(WidgetConfigContext);
  if (!config) {
    throw new Error('useWidgetConfig must be used within a WidgetConfigProvider');
  }
  return config;
};

// Widget Configuration Provider
const WidgetConfigProvider: React.FC<{ config: WidgetConfig; children: React.ReactNode }> = ({ config, children }) => {
  return (
    <WidgetConfigContext.Provider value={config}>
      {children}
    </WidgetConfigContext.Provider>
  );
};

interface WidgetWrapperProps {
  config: WidgetConfig;
}

// Internal chatbot component that runs inside Shadow DOM
const ShadowChatbot: React.FC<WidgetWrapperProps> = ({ config }) => {
  const { setViewMode, viewMode, unreadCount } = useChatStore();

  const handleOpenChat = () => {
    setViewMode(VIEW_MODE.CHATBOX);
  };

  useEffect(() => {
    if (config.autoOpen) {
      setViewMode(VIEW_MODE.CHATBOX);
    }
  }, [config.autoOpen, setViewMode]);

  useEffect(() => {
    if (config.theme === 'dark') {
      document.documentElement.setAttribute('data-theme', 'dark');
    } else {
      document.documentElement.removeAttribute('data-theme');
    }
  }, [config.theme]);


  return (
    <WidgetConfigProvider config={config}>
      <QueryProvider>
        <div
          className="chatbot-widget-container"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            pointerEvents: 'none',
            zIndex: config.zIndex || 10000,
            fontFamily: 'var(--font-family-sans)',
          }}
        >
          {/* Chat Container - only visible when not minimized */}
          {viewMode !== VIEW_MODE.MINIMIZED && (
            <div style={{ pointerEvents: 'auto', position: 'relative', zIndex: 1 }}>
              <ChatContainer />
            </div>
        )}

        {/* Floating Chat Button - only visible when minimized */}
        {viewMode === VIEW_MODE.MINIMIZED && (
          <div
            style={{
              pointerEvents: 'auto',
              position: 'fixed',
              bottom: '24px',
              right: '24px',
              zIndex: 10002
            }}
          >
            <FloatingChatButton
              isVisible={true}
              onClick={handleOpenChat}
              unreadCount={unreadCount}
            />
          </div>
        )}
      </div>
    </QueryProvider>
    </WidgetConfigProvider>
  );
};

// Shadow DOM wrapper component
export class ShadowDOMWrapper {
  private shadowRoot: ShadowRoot | null = null;
  private container: HTMLElement | null = null;
  private reactRoot: Root | null = null;
  private config: WidgetConfig;

  constructor(config: WidgetConfig) {
    this.config = config;
  }

  // Create the Shadow DOM container
  private createShadowContainer(): void {
    // Create container element
    this.container = document.createElement('div');
    this.container.id = 'eneco-chatbot-widget';

    // Set container styles to ensure proper positioning
    this.container.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      pointer-events: none !important;
      z-index: ${this.config.zIndex || 10000} !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
      overflow: hidden !important;
    `;

    // Create Shadow DOM
    this.shadowRoot = this.container.attachShadow({ mode: 'closed' });
    
    // Inject styles
    this.injectStyles();
    
    // Create React mount point
    const mountPoint = document.createElement('div');
    mountPoint.id = 'chatbot-react-root';
    mountPoint.style.cssText = `
      width: 100%;
      height: 100%;
      position: relative;
    `;
    
    this.shadowRoot.appendChild(mountPoint);
    
    // Append to body
    document.body.appendChild(this.container);
    
    // Create React root
    this.reactRoot = createRoot(mountPoint);
  }

  // Inject styles into Shadow DOM
  private injectStyles(): void {
    if (!this.shadowRoot) return;

    // Create style element
    const styleElement = document.createElement('style');

    // Get styles from the global CSS that will be injected by build process
    const stylesText = (window as any).__ENECO_CHATBOT_STYLES__ || '';

    styleElement.textContent = `
      /* Reset styles for Shadow DOM */
      *, *::before, *::after {
        box-sizing: border-box;
      }

      /* Inject main styles */
      ${stylesText}

      /* Additional widget-specific styles */
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }

      /* Ensure proper isolation */
      div, span, button, input, textarea {
        font-family: inherit;
      }

      /* Ensure floating button is properly positioned */
      .floating-chat-button {
        position: fixed !important;
        bottom: 24px !important;
        right: 24px !important;
        width: 68px !important;
        height: 68px !important;
        border-radius: 50% !important;
        border: 3px solid white !important;
        background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%) !important;
        box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important;
        cursor: pointer !important;
        z-index: 10002 !important;
        transition: all 0.2s ease !important;
        opacity: 1 !important;
        visibility: visible !important;
        outline: none !important;
      }

      .floating-chat-button:hover {
        transform: scale(1.08) !important;
        box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
        background: linear-gradient(135deg, #A855F7 0%, #8B5CF6 100%) !important;
      }

      .floating-chat-button:active {
        transform: scale(0.95) !important;
      }

      .floating-chat-button:focus {
        outline: none !important;
        box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.3), 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1) !important;
      }

      .floating-chat-button__avatar {
        position: relative !important;
        width: 100% !important;
        height: 100% !important;
        border-radius: 50% !important;
        overflow: hidden !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: white !important;
      }

      .floating-chat-button__image {
        width: 80% !important;
        height: 80% !important;
        object-fit: cover !important;
        border-radius: 50% !important;
      }

      /* Chat container styles */
      .chat-container {
        display: flex !important;
        flex-direction: column !important;
        background: white !important;
        border: 1px solid #e5e7eb !important;
        border-radius: 0.5rem !important;
        box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
        overflow: hidden !important;
        transition: all 0.2s ease !important;
      }

      .chat-container--chatbox {
        position: fixed !important;
        bottom: 24px !important;
        right: 24px !important;
        width: 440px !important;
        height: 65vh !important;
        z-index: 10000 !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 0.5rem !important;
        box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25) !important;
        background-clip: padding-box !important;
        transform: translateZ(0) !important;
        will-change: transform !important;
        transition: width 0.3s ease, height 0.3s ease, max-height 0.3s ease !important;
      }

      .chat-container--fullpage {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        height: 100vh !important;
        width: 100vw !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        z-index: 10001 !important;
      }
    `;

    this.shadowRoot.appendChild(styleElement);
  }

  // Mount the React component
  public mount(): void {
    if (!this.reactRoot) {
      this.createShadowContainer();
    }

    if (this.reactRoot) {
      this.reactRoot.render(
        <ShadowChatbot config={this.config} />
      );
    }
  }

  // Unmount and cleanup
  public unmount(): void {
    if (this.reactRoot) {
      this.reactRoot.unmount();
      this.reactRoot = null;
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
      this.shadowRoot = null;
    }
  }

  // Update configuration
  public updateConfig(newConfig: Partial<WidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.reactRoot) {
      this.reactRoot.render(
        <ShadowChatbot config={this.config} />
      );
    }
  }

  // Get current configuration
  public getConfig(): WidgetConfig {
    return { ...this.config };
  }

  // Check if mounted
  public isMounted(): boolean {
    return this.reactRoot !== null && this.container !== null;
  }
}

export default ShadowDOMWrapper;
