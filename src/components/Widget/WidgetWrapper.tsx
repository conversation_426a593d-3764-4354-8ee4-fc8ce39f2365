// Widget Wrapper Component - Handles Shadow DOM and isolation
import React, { useEffect } from 'react';
import { createRoot, type Root } from 'react-dom/client';
import ChatContainer from '../Chat/ChatContainer';
import FloatingChatButton from '../Chat/FloatingChatButton';
import QueryProvider from '../../providers/QueryProvider';
import { useChatStore } from '../../store';
import { VIEW_MODE } from '../../types';

// Styles will be injected at build time

interface WidgetConfig {
  apiUrl?: string;
  customerId?: string;
  accountId?: string;
  assistantName?: string;
  assistantTitle?: string;
  placeholder?: string;
  theme?: 'light' | 'dark';
  position?: 'bottom-right' | 'bottom-left';
  enableFullscreen?: boolean;
  autoOpen?: boolean;
  zIndex?: number;
}

interface WidgetWrapperProps {
  config: WidgetConfig;
}

// Internal chatbot component that runs inside Shadow DOM
const ShadowChatbot: React.FC<WidgetWrapperProps> = ({ config }) => {
  const { setViewMode, viewMode, unreadCount } = useChatStore();

  const handleOpenChat = () => {
    setViewMode(VIEW_MODE.CHATBOX);
  };

  // Auto-open if configured
  useEffect(() => {
    if (config.autoOpen) {
      setViewMode(VIEW_MODE.CHATBOX);
    }
  }, [config.autoOpen, setViewMode]);

  // Apply theme
  useEffect(() => {
    if (config.theme === 'dark') {
      document.documentElement.setAttribute('data-theme', 'dark');
    } else {
      document.documentElement.removeAttribute('data-theme');
    }
  }, [config.theme]);

  return (
    <QueryProvider>
      <div 
        className="chatbot-widget-container"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          pointerEvents: 'none',
          zIndex: 1,
          fontFamily: 'var(--font-family-sans)',
        }}
      >
        <div style={{ pointerEvents: 'auto' }}>
          <ChatContainer />
          <FloatingChatButton
            isVisible={viewMode === VIEW_MODE.MINIMIZED}
            onClick={handleOpenChat}
            unreadCount={unreadCount}
          />
        </div>
      </div>
    </QueryProvider>
  );
};

// Shadow DOM wrapper component
export class ShadowDOMWrapper {
  private shadowRoot: ShadowRoot | null = null;
  private container: HTMLElement | null = null;
  private reactRoot: Root | null = null;
  private config: WidgetConfig;

  constructor(config: WidgetConfig) {
    this.config = config;
  }

  // Create the Shadow DOM container
  private createShadowContainer(): void {
    // Create container element
    this.container = document.createElement('div');
    this.container.id = 'eneco-chatbot-widget';
    
    // Set container styles to ensure proper positioning
    this.container.style.cssText = `
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      pointer-events: none !important;
      z-index: ${this.config.zIndex || 9999} !important;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
    `;

    // Create Shadow DOM
    this.shadowRoot = this.container.attachShadow({ mode: 'closed' });
    
    // Inject styles
    this.injectStyles();
    
    // Create React mount point
    const mountPoint = document.createElement('div');
    mountPoint.id = 'chatbot-react-root';
    mountPoint.style.cssText = `
      width: 100%;
      height: 100%;
      position: relative;
    `;
    
    this.shadowRoot.appendChild(mountPoint);
    
    // Append to body
    document.body.appendChild(this.container);
    
    // Create React root
    this.reactRoot = createRoot(mountPoint);
  }

  // Inject styles into Shadow DOM
  private injectStyles(): void {
    if (!this.shadowRoot) return;

    // Create style element
    const styleElement = document.createElement('style');

    // Get styles from the global CSS that will be injected by build process
    const stylesText = (window as any).__ENECO_CHATBOT_STYLES__ || '';

    styleElement.textContent = `
      /* Reset styles for Shadow DOM */
      *, *::before, *::after {
        box-sizing: border-box;
      }

      /* Inject main styles */
      ${stylesText}

      /* Additional widget-specific styles */
      :host {
        all: initial;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }

      /* Ensure proper isolation */
      div, span, button, input, textarea {
        font-family: inherit;
      }
    `;

    this.shadowRoot.appendChild(styleElement);
  }

  // Mount the React component
  public mount(): void {
    if (!this.reactRoot) {
      this.createShadowContainer();
    }

    if (this.reactRoot) {
      this.reactRoot.render(
        <ShadowChatbot config={this.config} />
      );
    }
  }

  // Unmount and cleanup
  public unmount(): void {
    if (this.reactRoot) {
      this.reactRoot.unmount();
      this.reactRoot = null;
    }

    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
      this.container = null;
      this.shadowRoot = null;
    }
  }

  // Update configuration
  public updateConfig(newConfig: Partial<WidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.reactRoot) {
      this.reactRoot.render(
        <ShadowChatbot config={this.config} />
      );
    }
  }

  // Get current configuration
  public getConfig(): WidgetConfig {
    return { ...this.config };
  }

  // Check if mounted
  public isMounted(): boolean {
    return this.reactRoot !== null && this.container !== null;
  }
}

export default ShadowDOMWrapper;
