// Vite plugin to inline all assets and CSS for widget build
import type { Plugin } from 'vite';
import { readFileSync } from 'fs';
import { resolve } from 'path';

export function inlineAssetsPlugin(): Plugin {
  let cssContent = '';

  return {
    name: 'inline-assets',
    apply: 'build',
    generateBundle(_options, bundle) {
      // Extract CSS content first
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        if (chunk.type === 'asset' && fileName.endsWith('.css')) {
          cssContent += chunk.source;
          delete bundle[fileName]; // Remove CSS file from bundle
        }
      });

      // Process all chunks to inline assets and CSS
      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];

        if (chunk.type === 'chunk') {
          // Replace asset imports with base64 data URLs
          chunk.code = chunk.code.replace(
            /import\s+(\w+)\s+from\s+['"]([^'"]+\.(png|jpg|jpeg|gif|svg))['"];?/g,
            (match, varName, assetPath) => {
              try {
                const fullPath = resolve(process.cwd(), 'src', assetPath);
                const buffer = readFileSync(fullPath);
                const base64 = buffer.toString('base64');
                const mimeType = getMimeType(assetPath);
                const dataUrl = `data:${mimeType};base64,${base64}`;
                
                return `const ${varName} = "${dataUrl}";`;
              } catch (error) {
                console.warn(`Failed to inline asset: ${assetPath}`, error);
                return match;
              }
            }
          );

          // Replace dynamic imports of assets
          chunk.code = chunk.code.replace(
            /new\s+URL\(['"]([^'"]+\.(png|jpg|jpeg|gif|svg))['"],\s*import\.meta\.url\)/g,
            (match, assetPath) => {
              try {
                const fullPath = resolve(process.cwd(), 'src', assetPath);
                const buffer = readFileSync(fullPath);
                const base64 = buffer.toString('base64');
                const mimeType = getMimeType(assetPath);
                const dataUrl = `data:${mimeType};base64,${base64}`;
                
                return `"${dataUrl}"`;
              } catch (error) {
                console.warn(`Failed to inline dynamic asset: ${assetPath}`, error);
                return match;
              }
            }
          );

          // Inject CSS into the chunk
          if (cssContent) {
            chunk.code = `
              // Inject CSS styles
              window.__ENECO_CHATBOT_STYLES__ = ${JSON.stringify(cssContent)};

              ${chunk.code}
            `;
          }
        }
      });
    }
  };
}

function getMimeType(filePath: string): string {
  const ext = filePath.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case 'png': return 'image/png';
    case 'jpg':
    case 'jpeg': return 'image/jpeg';
    case 'gif': return 'image/gif';
    case 'svg': return 'image/svg+xml';
    default: return 'application/octet-stream';
  }
}
