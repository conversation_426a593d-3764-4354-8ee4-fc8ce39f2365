import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { inlineAssetsPlugin } from './vite-plugins/inline-assets'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isWidget = mode === 'widget';

  return {
    plugins: [
      react(),
      ...(isWidget ? [
        inlineAssetsPlugin(),
        // Plugin to handle SCSS inline imports
        {
          name: 'scss-inline',
          load(id: string) {
            if (id.endsWith('?inline')) {
              return `export default ${JSON.stringify('')};`; // Will be processed by SCSS
            }
          },
          transform(code: string, id: string) {
            if (id.endsWith('.scss?inline')) {
              // Return the CSS as a string export
              return `export default ${JSON.stringify(code)};`;
            }
          }
        }
      ] : [])
    ],

    // Define environment variables for widget build
    define: isWidget ? {
      'import.meta.env.DEV': 'false',
      'import.meta.env.PROD': 'true',
      'import.meta.env.VITE_API_BASE_URL': '"/api"',
      'import.meta.env.VITE_USE_MOCK_API': '"false"',
      'import.meta.env.VITE_API_KEY': 'undefined',
      'import.meta.env.VITE_AUTH_TOKEN': 'undefined',
      'process.env.NODE_ENV': '"production"',
    } : {},

    // Different build configurations
    build: isWidget ? {
      // Widget build configuration
      lib: {
        entry: resolve(__dirname, 'src/widget.tsx'),
        name: 'EnecoChatbot',
        fileName: 'eneco-chatbot-widget',
        formats: ['iife']
      },
      rollupOptions: {
        output: {
          // Ensure everything is bundled into a single file
          inlineDynamicImports: true,
          manualChunks: undefined,
        },
        // Don't externalize any dependencies - bundle everything
        external: [],
      },
      // Inline all CSS
      cssCodeSplit: false,
      // Ensure assets are inlined
      assetsInlineLimit: 100000000, // 100MB - inline everything
    } : {
      // Regular app build configuration
      rollupOptions: {
        input: {
          main: resolve(__dirname, 'index.html'),
        },
      },
    },

    server: {
      proxy: {
        // Proxy API requests to avoid CORS issues in development
        '/api': {
          target: 'https://bot-v1.acc.api-digital.enecogroup.com',
          changeOrigin: true,
          secure: true,
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('proxy error', err);
            });
            proxy.on('proxyReq', (_proxyReq, req, _res) => {
              console.log('Sending Request to the Target:', req.method, req.url);
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
            });
          },
        }
      }
    },

    // Ensure assets are properly handled
    assetsInclude: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg'],
  }
})
